# New-API 日志功能详解

## 📋 概述

New-API项目的日志功能是一个完整的日志记录、查询和管理系统，主要用于记录用户的API调用、配额消费、错误信息等关键业务数据。

## 🏗️ 架构设计

### 文件结构
```
├── model/log.go           # 核心日志模型和数据库操作
├── controller/log.go      # HTTP控制器层
├── router/api-router.go   # 路由配置
├── relay/relay-text.go    # 实际调用日志记录
├── model/main.go          # 数据库初始化
├── common/logger.go       # 系统日志工具
└── middleware/logger.go   # HTTP请求日志中间件
```

## 📊 数据结构

### Log 结构体 (`model/log.go:17-37`)

```go
type Log struct {
    Id               int    `json:"id" gorm:"index:idx_created_at_id,priority:1"`
    UserId           int    `json:"user_id" gorm:"index"`
    CreatedAt        int64  `json:"created_at" gorm:"bigint;index:idx_created_at_id,priority:2;index:idx_created_at_type"`
    Type             int    `json:"type" gorm:"index:idx_created_at_type"`
    Content          string `json:"content"`
    Username         string `json:"username" gorm:"index;index:index_username_model_name,priority:2;default:''"`
    TokenName        string `json:"token_name" gorm:"index;default:''"`
    ModelName        string `json:"model_name" gorm:"index;index:index_username_model_name,priority:1;default:''"`
    Quota            int    `json:"quota" gorm:"default:0"`
    PromptTokens     int    `json:"prompt_tokens" gorm:"default:0"`
    CompletionTokens int    `json:"completion_tokens" gorm:"default:0"`
    UseTime          int    `json:"use_time" gorm:"default:0"`
    IsStream         bool   `json:"is_stream"`
    ChannelId        int    `json:"channel" gorm:"index"`
    ChannelName      string `json:"channel_name" gorm:"->"`
    TokenId          int    `json:"token_id" gorm:"default:0;index"`
    Group            string `json:"group" gorm:"index"`
    Ip               string `json:"ip" gorm:"index;default:''"`
    Other            string `json:"other"`
}
```

### 字段说明

| 字段名 | 类型 | 说明 |
|--------|------|------|
| Id | int | 日志唯一标识 |
| UserId | int | 用户ID |
| CreatedAt | int64 | 创建时间戳 |
| Type | int | 日志类型（见下方类型说明） |
| Content | string | 日志内容描述 |
| Username | string | 用户名 |
| TokenName | string | 使用的Token名称 |
| ModelName | string | AI模型名称 |
| Quota | int | 消耗的配额 |
| PromptTokens | int | 输入Token数量 |
| CompletionTokens | int | 输出Token数量 |
| UseTime | int | 请求耗时（秒） |
| IsStream | bool | 是否为流式请求 |
| ChannelId | int | 渠道ID |
| TokenId | int | Token ID |
| Group | string | 用户组 |
| Ip | string | 客户端IP地址 |
| Other | string | 其他信息（JSON格式） |
| PromptData | string | 用户请求的原始数据（可选记录） |
| CompletionData | string | 模型响应的原始数据（可选记录） |

### 日志类型 (`model/log.go:39-46`)

```go
const (
    LogTypeUnknown = iota  // 0 - 未知类型
    LogTypeTopup          // 1 - 充值记录
    LogTypeConsume        // 2 - 消费记录
    LogTypeManage         // 3 - 管理操作
    LogTypeSystem         // 4 - 系统日志
    LogTypeError          // 5 - 错误日志
)
```

## 🔧 核心功能

### 1. 日志记录功能

#### 基础日志记录 (`model/log.go:76-92`)
```go
func RecordLog(userId int, logType int, content string)
```
- 用于记录简单的日志信息
- 自动获取用户名和时间戳

#### 消费日志记录 (`model/log.go:152-193`)
```go
func RecordConsumeLog(c *gin.Context, userId int, params RecordConsumeLogParams)
```
- 记录详细的API调用消费信息
- 包含Token使用量、模型信息、耗时等
- 支持IP记录（根据用户设置）

#### 错误日志记录 (`model/log.go:94-134`)
```go
func RecordErrorLog(c *gin.Context, userId int, channelId int, modelName string, ...)
```
- 专门记录错误信息
- 包含完整的请求上下文

### 2. 日志查询功能

#### 管理员查询 (`model/log.go:201-304`)
```go
func GetAllLogs(logType int, startTimestamp int64, endTimestamp int64, 
                modelName string, username string, tokenName string, 
                startIdx int, num int, channel int, group string) (logs []*Log, total int64, err error)
```

**支持的筛选条件：**
- 日志类型
- 时间范围（开始/结束时间戳）
- 模型名称（模糊匹配）
- 用户名
- Token名称
- 渠道ID
- 用户组
- 分页参数

#### 用户查询 (`model/log.go:270-304`)
```go
func GetUserLogs(userId int, logType int, startTimestamp int64, endTimestamp int64, 
                 modelName string, tokenName string, startIdx int, num int, group string) (logs []*Log, total int64, err error)
```
- 只能查询自己的日志
- 支持类似的筛选条件

#### 搜索功能
- `SearchAllLogs()` - 管理员全局搜索
- `SearchUserLogs()` - 用户个人搜索
- 支持关键词匹配

### 3. 统计功能 (`model/log.go:323-388`)

```go
func SumUsedQuota(logType int, startTimestamp int64, endTimestamp int64, 
                  modelName string, username string, tokenName string, 
                  channel int, group string) (stat Stat)
```

**统计指标：**
- Quota: 总配额消耗
- RPM: 每分钟请求数
- TPM: 每分钟Token数

### 4. 日志管理功能

#### 删除历史日志 (`model/log.go:390-411`)
```go
func DeleteOldLog(ctx context.Context, targetTimestamp int64, limit int) (int64, error)
```
- 批量删除指定时间之前的日志
- 支持分批删除，避免长时间锁表

## 🌐 API接口

### 路由配置 (`router/api-router.go:146-153`)

| 方法 | 路径 | 权限 | 功能 |
|------|------|------|------|
| GET | `/api/log/` | 管理员 | 获取所有日志 |
| DELETE | `/api/log/` | 管理员 | 删除历史日志 |
| GET | `/api/log/stat` | 管理员 | 获取日志统计 |
| GET | `/api/log/self/stat` | 用户 | 获取个人统计 |
| GET | `/api/log/search` | 管理员 | 搜索日志 |
| GET | `/api/log/self` | 用户 | 获取个人日志 |
| GET | `/api/log/self/search` | 用户 | 搜索个人日志 |

### 请求参数

#### 查询参数
- `type`: 日志类型
- `start_timestamp`: 开始时间戳
- `end_timestamp`: 结束时间戳
- `username`: 用户名
- `token_name`: Token名称
- `model_name`: 模型名称
- `channel`: 渠道ID
- `group`: 用户组
- `keyword`: 搜索关键词
- `page`: 页码
- `size`: 每页大小

#### 删除参数
- `target_timestamp`: 删除此时间之前的日志

## 🔍 使用示例

### 在业务代码中记录日志

```go
// 记录消费日志 (relay/relay-text.go:556-570)
model.RecordConsumeLog(ctx, relayInfo.UserId, model.RecordConsumeLogParams{
    ChannelId:        relayInfo.ChannelId,
    PromptTokens:     promptTokens,
    CompletionTokens: completionTokens,
    ModelName:        logModel,
    TokenName:        tokenName,
    Quota:            quota,
    Content:          logContent,
    TokenId:          relayInfo.TokenId,
    UserQuota:        userQuota,
    UseTimeSeconds:   int(useTimeSeconds),
    IsStream:         relayInfo.IsStream,
    Group:            relayInfo.UsingGroup,
    Other:            other,
})
```

## 🗄️ 数据库设计

### 索引策略
- 主键索引：`id`
- 复合索引：`(created_at, id)` - 用于时间范围查询和分页
- 复合索引：`(created_at, type)` - 用于按类型和时间查询
- 复合索引：`(username, model_name)` - 用于用户和模型维度查询
- 单字段索引：`user_id`, `token_name`, `model_name`, `channel_id`, `token_id`, `group`, `ip`

### 数据库分离
- 支持独立的日志数据库（通过 `LOG_SQL_DSN` 环境变量配置）
- 可与主业务数据库分离，提高性能

## ⚙️ 配置选项

### 环境变量
- `LOG_SQL_DSN`: 日志数据库连接字符串（可选，默认使用主数据库）
- `LOG_CONSUME_ENABLED`: 是否启用消费日志记录
- `RECORD_PROMPT_DATA`: 是否记录用户请求数据（默认false）
- `RECORD_COMPLETION_DATA`: 是否记录模型响应数据（默认false）
- `MAX_PROMPT_DATA_LENGTH`: 请求数据最大长度（默认10240字节）
- `MAX_COMPLETION_DATA_LENGTH`: 响应数据最大长度（默认10240字节）

### 用户设置
- `RecordIpLog`: 用户级别的IP记录开关

## � 新增功能：请求响应数据记录

### 功能概述
新增了记录用户请求数据（PromptData）和模型响应数据（CompletionData）的功能，用于：
- 调试和问题排查
- 数据分析和模型效果评估
- 用户行为分析
- 合规性审计

### 配置方式
```bash
# 启用请求数据记录
RECORD_PROMPT_DATA=true

# 启用响应数据记录
RECORD_COMPLETION_DATA=true

# 设置数据长度限制（字节）
MAX_PROMPT_DATA_LENGTH=10240
MAX_COMPLETION_DATA_LENGTH=10240
```

### 数据处理机制
1. **条件记录**：只有在配置启用时才记录数据
2. **长度限制**：超过限制的数据会被自动截断
3. **数据库存储**：使用LONGTEXT类型存储，支持大量数据（无默认值，符合MySQL规范）
4. **性能考虑**：记录过程不影响主业务流程
5. **统一处理**：通过IOCopyBytesGracefully函数统一处理非流式响应
6. **流式支持**：特别处理流式响应，收集完整的响应内容

### 安全考虑
- **默认关闭**：出于安全考虑，功能默认关闭
- **敏感数据**：可能包含用户隐私信息，需谨慎启用
- **访问控制**：建议配合适当的访问权限控制
- **数据保护**：遵循相关数据保护法规

## �🚀 性能优化

1. **数据库索引优化**：针对常用查询条件建立复合索引
2. **分批删除**：历史日志删除采用分批处理，避免长时间锁表
3. **数据库分离**：支持日志数据库独立部署
4. **异步处理**：部分日志记录操作可异步执行
5. **数据截断**：新增数据记录功能包含长度限制，控制存储成本

## 📝 注意事项

1. **IP记录**：根据用户设置决定是否记录IP地址
2. **配额控制**：消费日志记录会检查 `LOG_CONSUME_ENABLED` 配置
3. **错误处理**：日志记录失败不会影响主业务流程
4. **数据一致性**：日志记录与配额扣除保持一致性
5. **敏感数据**：PromptData和CompletionData可能包含敏感信息，默认关闭
6. **存储成本**：启用数据记录会显著增加数据库存储空间
7. **性能影响**：大量数据记录可能影响系统性能
8. **数据截断**：超过长度限制的数据会被自动截断

## 🔧 故障排除

### 数据库迁移错误
如果遇到类似错误：
```
BLOB, TEXT, GEOMETRY or JSON column 'prompt_data' can't have a default value
```

**原因**：MySQL不允许LONGTEXT字段设置默认值
**解决**：已在代码中修复，移除了LONGTEXT字段的默认值设置

### 配置不生效
1. 确认环境变量正确设置
2. 重启应用程序
3. 检查配置文件格式

### 数据未记录
1. 确认相关配置已启用
2. 检查数据库连接正常
3. 查看系统日志错误信息

### 响应数据为空
1. **流式响应**：确保使用支持的模型和渠道
2. **非流式响应**：通过IOCopyBytesGracefully统一处理
3. **渠道兼容性**：已支持OpenAI、Claude等主要渠道
4. **数据长度**：检查是否超过长度限制被截断

---

*本文档基于 New-API 项目代码分析生成，详细实现请参考对应源码文件。*
